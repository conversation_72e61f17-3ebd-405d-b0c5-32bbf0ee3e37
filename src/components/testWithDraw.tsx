import { mnemonicToPrivate<PERSON><PERSON> } from "@ton/crypto";
import { TonClient, WalletContractV4, internal } from "@ton/ton";

const receiver = "UQAW8pzkwoPgsiKi6f-RVvE-VXOoHJ_dhEZ5-HkES7NdIlSQ";

const MARKETPLACE_WALLET_MNEMONIC =
  "spice myth collect display ship legend seminar injury setup voice faith steel erase captain supply cave range author neither decrease hire update mechanic state";

export const testWithdraw = async () => {
  try {
    const tonRpcUrl = "https://misty-crimson-friday.ton-mainnet.quiknode.pro";
    const client = new TonClient({
      endpoint: tonRpcUrl,
    });

    const marketplaceMnemonic = MARKETPLACE_WALLET_MNEMONIC;
    const keyPair = await mnemonicToPrivateKey(marketplaceMnemonic.split(" "));

    console.log("Key pair:", keyPair);

    const workchain = 0;
    const marketplaceWallet = WalletContractV4.create({
      workchain,
      publicKey: keyPair.publicKey,
    });
    const marketplaceContract = client.open(marketplaceWallet);

    const seqno = await marketplaceContract.getSeqno();

    const amountInNanotons = Math.floor(1 * 1000000000);

    const transfer = marketplaceContract.createTransfer({
      seqno,
      secretKey: keyPair.secretKey,
      messages: [
        internal({
          value: amountInNanotons.toString(),
          to: receiver,
          body: "Withdrawal from marketplace",
        }),
      ],
    });

    await marketplaceContract.send(transfer);
  } catch (error) {
    debugger;
    console.error("Error in testWithdraw function:", error);
  }
};
